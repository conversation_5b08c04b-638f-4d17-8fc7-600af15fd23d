module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2022: true // Node 18 支持更新的ES标准
  },
  extends: [
    'plugin:vue/essential',
    'eslint:recommended'
  ],
  parserOptions: {
    parser: '@babel/eslint-parser',
    requireConfigFile: false,
    ecmaVersion: 2022, // 更新到2022
    sourceType: 'module'
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-unused-vars': ['warn', { 
      "argsIgnorePattern": "^_",
      "varsIgnorePattern": "^_" 
    }],
    'vue/no-unused-components': 'warn'
  },
  globals: {
    process: 'readonly',
    Buffer: 'readonly',
    globalThis: 'readonly'
  }
}