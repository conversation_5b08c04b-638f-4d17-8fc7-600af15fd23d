/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: #2c3e50;
  background-color: #f5f7fa;
}

/* 重置一些Element UI样式 */
.el-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
  border-radius: 8px;
}

.el-card__header {
  padding: 18px 20px;
  background-color: #fff;
  border-bottom: 1px solid #f0f2f5;
  font-weight: 500;
}

.el-card__body {
  padding: 20px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 工具提示样式 */
.tooltip-content {
  max-width: 300px;
  word-wrap: break-word;
}

/* 响应式辅助类 */
.hidden-xs {
  display: block;
}

.hidden-sm {
  display: block;
}

.hidden-md {
  display: block;
}

.hidden-lg {
  display: block;
}

@media (max-width: 767px) {
  .hidden-xs {
    display: none !important;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .hidden-sm {
    display: none !important;
  }
}

@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-md {
    display: none !important;
  }
}

@media (min-width: 1200px) {
  .hidden-lg {
    display: none !important;
  }
}

/* 文本辅助类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-muted {
  color: #999;
}

.text-primary {
  color: #409eff;
}

.text-success {
  color: #67c23a;
}

.text-warning {
  color: #e6a23c;
}

.text-danger {
  color: #f56c6c;
}

/* 间距辅助类 */
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 8px; }
.ml-2 { margin-left: 16px; }
.ml-3 { margin-left: 24px; }
.ml-4 { margin-left: 32px; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 8px; }
.mr-2 { margin-right: 16px; }
.mr-3 { margin-right: 24px; }
.mr-4 { margin-right: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

/* 动画类 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-enter-active, .slide-leave-active {
  transition: all 0.3s;
}

.slide-enter, .slide-leave-to {
  transform: translateX(10px);
  opacity: 0;
}

/* 自定义组件样式 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 代码样式 */
.code-block {
  background-color: #f8f8f8;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  overflow-x: auto;
}

/* 标签样式增强 */
.el-tag {
  border-radius: 12px;
  font-size: 12px;
}

.el-tag--mini {
  height: 20px;
  line-height: 18px;
  font-size: 11px;
  padding: 0 8px;
}

/* 按钮组增强 */
.el-button-group .el-button {
  margin: 0;
}

/* 表单增强 */
.el-form-item__label {
  font-weight: 500;
  color: #606266;
}

.el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 卡片头部增强 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.card-header i {
  margin-right: 8px;
  color: #409eff;
}

/* 消息类型样式 */
.message-type-system {
  color: #909399;
  background-color: #f4f4f5;
}

.message-type-success {
  color: #67c23a;
  background-color: #f0f9ff;
}

.message-type-error {
  color: #f56c6c;
  background-color: #fef0f0;
}

.message-type-warning {
  color: #e6a23c;
  background-color: #fdf6ec;
}

.message-type-sent {
  color: #409eff;
  background-color: #ecf5ff;
}

.message-type-received {
  color: #67c23a;
  background-color: #f0f9ff;
}

/* 统计卡片样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  margin: 15px 0;
}

.stats-card {
  text-align: center;
  padding: 15px;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
  display: block;
}

.stats-label {
  font-size: 12px;
  color: #666;
  margin: 0;
}

/* 工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 15px;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
  opacity: 0.5;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 徽章增强 */
.el-badge__content {
  border-radius: 10px;
  font-size: 11px;
  height: 18px;
  line-height: 18px;
  padding: 0 6px;
}

/* 分割线增强 */
.el-divider {
  margin: 20px 0;
}

.el-divider__text {
  padding: 0 15px;
  font-size: 12px;
  color: #909399;
}

/* 对话框增强 */
.el-dialog {
  border-radius: 8px;
}

.el-dialog__header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #f0f2f5;
}

.el-dialog__body {
  padding: 20px;
}

/* 选择器增强 */
.el-select-dropdown {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 下拉菜单增强 */
.el-dropdown-menu {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 工具提示增强 */
.el-tooltip__popper {
  border-radius: 4px;
  font-size: 12px;
}

/* 进度条增强 */
.el-progress-bar__outer {
  border-radius: 10px;
}

.el-progress-bar__inner {
  border-radius: 10px;
}

/* 开关增强 */
.el-switch {
  font-size: 12px;
}

/* 表格增强 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

.el-table th {
  background-color: #fafafa;
  font-weight: 500;
}

/* 分页增强 */
.el-pagination {
  text-align: center;
  margin-top: 20px;
}

/* 响应式增强 */
@media (max-width: 768px) {
  .el-card__body {
    padding: 15px;
  }
  
  .el-form-item {
    margin-bottom: 18px;
  }
  
  .el-form-item__label {
    line-height: 32px;
    margin-bottom: 5px;
  }
  
  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .el-card__header {
    padding: 15px;
  }
  
  .el-card__body {
    padding: 15px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .el-button-group {
    flex-wrap: wrap;
  }
  
  .el-button-group .el-button {
    margin: 2px;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .el-card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
  
  body {
    background-color: white;
  }
}