<template>
  <div class="connection-indicator">
    <div :class="['indicator-dot', statusClass]"></div>
    <span class="indicator-text">{{ statusText }}</span>
  </div>
</template>

<script>
export default {
  name: 'ConnectionIndicator',
  
  props: {
    status: {
      type: String,
      default: 'disconnected',
      validator: value => ['disconnected', 'connecting', 'connected', 'error'].includes(value)
    }
  },
  
  computed: {
    statusClass() {
      return this.status
    },
    
    statusText() {
      const statusMap = {
        disconnected: '未连接',
        connecting: '连接中',
        connected: '已连接',
        error: '连接错误'
      }
      return statusMap[this.status] || '未知'
    }
  }
}
</script>

<style scoped>
.connection-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.indicator-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  position: relative;
}

.indicator-dot.connected {
  background-color: #67c23a;
  animation: pulse 2s infinite;
}

.indicator-dot.connecting {
  background-color: #e6a23c;
  animation: blink 1s infinite;
}

.indicator-dot.disconnected {
  background-color: #909399;
}

.indicator-dot.error {
  background-color: #f56c6c;
  animation: shake 0.5s infinite;
}

.indicator-text {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.5;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-2px);
  }
  75% {
    transform: translateX(2px);
  }
}
</style>