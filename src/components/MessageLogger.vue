<template>
  <el-card class="logger-card">
    <div slot="header" class="logger-header">
      <span><i class="el-icon-document"></i> 消息日志</span>
      <div class="header-controls">
        <el-badge :value="messages.length" :max="999" class="message-count-badge">
          <span style="font-size: 12px; color: #666;">总计</span>
        </el-badge>
        <el-button-group size="mini">
          <el-button 
            @click="toggleAutoScroll"
            :type="autoScroll ? 'primary' : 'default'"
            icon="el-icon-bottom"
            title="自动滚动">
            {{ autoScroll ? '停止' : '自动' }}
          </el-button>
          <el-button 
            @click="pauseLogging"
            :type="isPaused ? 'warning' : 'default'"
            :icon="isPaused ? 'el-icon-video-play' : 'el-icon-video-pause'"
            title="暂停/恢复日志">
            {{ isPaused ? '恢复' : '暂停' }}
          </el-button>
          <el-button 
            @click="exportLogs"
            icon="el-icon-download"
            title="导出日志">
            导出
          </el-button>
          <el-button 
            @click="clearMessages"
            icon="el-icon-delete"
            title="清空日志">
            清空
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <!-- 过滤控制 -->
    <div class="filter-controls">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-select 
            v-model="filterType" 
            placeholder="消息类型" 
            size="small" 
            style="width: 100%"
            @change="applyFilters">
            <el-option label="全部类型" value="all"></el-option>
            <el-option label="系统消息" value="system"></el-option>
            <el-option label="成功消息" value="success"></el-option>
            <el-option label="错误消息" value="error"></el-option>
            <el-option label="警告消息" value="warning"></el-option>
            <el-option label="发送消息" value="sent"></el-option>
            <el-option label="接收消息" value="received"></el-option>
          </el-select>
        </el-col>
        
        <el-col :span="8">
          <el-input 
            v-model="searchKeyword" 
            placeholder="搜索内容..." 
            size="small"
            @input="applyFilters"
            clearable>
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </el-col>
        
        <el-col :span="8">
          <el-input 
            v-model="topicFilter" 
            placeholder="过滤主题..." 
            size="small"
            @input="applyFilters"
            clearable>
            <i slot="prefix" class="el-input__icon el-icon-location"></i>
          </el-input>
        </el-col>
      </el-row>
      
      <!-- 快速过滤 -->
      <div class="quick-filters">
        <el-tag 
          v-for="type in messageTypes"
          :key="type.value"
          :type="type.tagType"
          size="mini"
          :class="{ 'filter-active': filterType === type.value }"
          @click="setQuickFilter(type.value)"
          class="quick-filter-tag">
          {{ type.label }} ({{ getTypeCount(type.value) }})
        </el-tag>
      </div>
    </div>
    
    <!-- 消息列表 -->
    <div class="messages-container" ref="messagesContainer">
      <div 
        v-for="message in filteredMessages" 
        :key="message.id"
        :class="['message-item', message.type]"
        @click="selectMessage(message)">
        
        <div class="message-header">
          <div class="message-meta">
            <el-tag 
              :type="getMessageTagType(message.type)" 
              size="mini" 
              class="message-type-tag">
              {{ getMessageTypeLabel(message.type) }}
            </el-tag>
            <span class="message-time">{{ message.time }}</span>
            <span v-if="message.topic" class="message-topic">[{{ message.topic }}]</span>
          </div>
          <div class="message-actions">
            <el-button 
              size="mini" 
              type="text" 
              @click.stop="copyMessage(message)"
              icon="el-icon-document-copy"
              title="复制消息">
            </el-button>
            <el-button 
              size="mini" 
              type="text" 
              @click.stop="viewMessageDetail(message)"
              icon="el-icon-view"
              title="查看详情">
            </el-button>
          </div>
        </div>
        
        <div class="message-content">
          <span v-if="message.content.length <= maxContentLength">{{ message.content }}</span>
          <span v-else>
            {{ message.content.substring(0, maxContentLength) }}
            <el-button 
              type="text" 
              size="mini" 
              @click.stop="viewMessageDetail(message)"
              style="color: #409eff; padding: 0; margin-left: 5px;">
              ...查看更多
            </el-button>
          </span>
        </div>
      </div>
      
      <!-- 暂停提示 -->
      <div v-if="isPaused" class="pause-indicator">
        <i class="el-icon-video-pause"></i>
        <span>日志记录已暂停</span>
      </div>
      
      <!-- 空状态 -->
      <div v-if="filteredMessages.length === 0 && !isPaused" class="no-messages">
        <i class="el-icon-info"></i>
        <p v-if="messages.length === 0">暂无消息日志</p>
        <p v-else>没有符合条件的消息</p>
      </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="logger-stats">
      <div class="stats-item">
        <span class="stats-label">显示:</span>
        <span class="stats-value">{{ filteredMessages.length }} / {{ messages.length }}</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">速率:</span>
        <span class="stats-value">{{ messageRate }} 条/分钟</span>
      </div>
      <div class="stats-item">
        <span class="stats-label">存储:</span>
        <span class="stats-value">{{ formatSize(estimatedSize) }}</span>
      </div>
    </div>
    
    <!-- 消息详情对话框 -->
    <el-dialog 
      :title="selectedMessage ? '消息详情' : ''"
      :visible.sync="detailDialogVisible"
      width="60%"
      :modal-append-to-body="false">
      
      <div v-if="selectedMessage" class="message-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="类型">
            <el-tag :type="getMessageTagType(selectedMessage.type)">
              {{ getMessageTypeLabel(selectedMessage.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="时间">
            {{ selectedMessage.time }}
          </el-descriptions-item>
          <el-descriptions-item label="主题" v-if="selectedMessage.topic">
            <el-tag type="info">{{ selectedMessage.topic }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="大小">
            {{ formatSize(new Blob([selectedMessage.content]).size) }}
          </el-descriptions-item>
        </el-descriptions>
        
        <el-divider content-position="left">消息内容</el-divider>
        
        <div class="message-detail-content">
          <el-input 
            type="textarea" 
            :value="selectedMessage.content"
            :rows="10"
            readonly>
          </el-input>
        </div>
        
        <div class="detail-actions">
          <el-button @click="copyMessage(selectedMessage)" icon="el-icon-document-copy">
            复制内容
          </el-button>
          <el-button @click="downloadMessage" icon="el-icon-download">
            下载消息
          </el-button>
          <el-button 
            v-if="isValidJson(selectedMessage.content)"
            @click="formatJson" 
            icon="el-icon-magic-stick">
            格式化JSON
          </el-button>
        </div>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
export default {
  name: 'MessageLogger',
  
  props: {
    messages: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      autoScroll: true,
      isPaused: false,
      filterType: 'all',
      searchKeyword: '',
      topicFilter: '',
      maxContentLength: 100,
      selectedMessage: null,
      detailDialogVisible: false,
      
      messageTypes: [
        { value: 'all', label: '全部', tagType: '' },
        { value: 'system', label: '系统', tagType: 'info' },
        { value: 'success', label: '成功', tagType: 'success' },
        { value: 'error', label: '错误', tagType: 'danger' },
        { value: 'warning', label: '警告', tagType: 'warning' },
        { value: 'sent', label: '发送', tagType: 'primary' },
        { value: 'received', label: '接收', tagType: 'success' }
      ],
      
      filteredMessages: [],
      messageTimestamps: []
    }
  },
  
  computed: {
    messageRate() {
      const now = Date.now()
      const oneMinuteAgo = now - 60000
      const recentMessages = this.messageTimestamps.filter(timestamp => timestamp > oneMinuteAgo)
      return recentMessages.length
    },
    
    estimatedSize() {
      return this.messages.reduce((total, msg) => {
        return total + new Blob([JSON.stringify(msg)]).size
      }, 0)
    }
  },
  
  watch: {
    messages: {
      handler() {
        if (!this.isPaused) {
          this.applyFilters()
          this.updateMessageTimestamps()
          
          if (this.autoScroll) {
            this.$nextTick(() => {
              this.scrollToBottom()
            })
          }
        }
      },
      deep: true
    }
  },
  
  mounted() {
    this.applyFilters()
    
    // 定期清理旧的时间戳
    setInterval(() => {
      const oneHourAgo = Date.now() - 3600000
      this.messageTimestamps = this.messageTimestamps.filter(timestamp => timestamp > oneHourAgo)
    }, 300000)
  },
  
  methods: {
    applyFilters() {
      let filtered = [...this.messages]
      
      // 类型过滤
      if (this.filterType !== 'all') {
        filtered = filtered.filter(msg => msg.type === this.filterType)
      }
      
      // 关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(msg => 
          msg.content.toLowerCase().includes(keyword)
        )
      }
      
      // 主题过滤
      if (this.topicFilter) {
        const topicKeyword = this.topicFilter.toLowerCase()
        filtered = filtered.filter(msg => 
          msg.topic && msg.topic.toLowerCase().includes(topicKeyword)
        )
      }
      
      this.filteredMessages = filtered
    },
    
    setQuickFilter(type) {
      this.filterType = type
      this.applyFilters()
    },
    
    getTypeCount(type) {
      if (type === 'all') return this.messages.length
      return this.messages.filter(msg => msg.type === type).length
    },
    
    getMessageTagType(type) {
      const typeMap = {
        system: 'info',
        success: 'success',
        error: 'danger',
        warning: 'warning',
        sent: 'primary',
        received: 'success'
      }
      return typeMap[type] || 'info'
    },
    
    getMessageTypeLabel(type) {
      const labelMap = {
        system: '系统',
        success: '成功',
        error: '错误',
        warning: '警告',
        sent: '发送',
        received: '接收'
      }
      return labelMap[type] || type
    },
    
    toggleAutoScroll() {
      this.autoScroll = !this.autoScroll
      if (this.autoScroll) {
        this.scrollToBottom()
      }
    },
    
    pauseLogging() {
      this.isPaused = !this.isPaused
      if (!this.isPaused) {
        this.applyFilters()
      }
    },
    
    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },
    
    selectMessage(message) {
      this.selectedMessage = message
    },
    
    viewMessageDetail(message) {
      this.selectedMessage = message
      this.detailDialogVisible = true
    },
    
    copyMessage(message) {
      try {
        const textToCopy = `[${message.time}] ${message.type.toUpperCase()}: ${message.content}`
        
        if (navigator.clipboard) {
          navigator.clipboard.writeText(textToCopy).then(() => {
            this.$message.success('消息已复制到剪贴板')
          })
        } else {
          // 备用方案
          const textArea = document.createElement('textarea')
          textArea.value = textToCopy
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
          this.$message.success('消息已复制到剪贴板')
        }
      } catch (error) {
        this.$message.error('复制失败')
      }
    },
    
    downloadMessage() {
      if (!this.selectedMessage) return
      
      const content = JSON.stringify(this.selectedMessage, null, 2)
      const blob = new Blob([content], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      
      const a = document.createElement('a')
      a.href = url
      a.download = `message_${this.selectedMessage.timestamp || Date.now()}.json`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    },
    
    exportLogs() {
      const logsToExport = this.filteredMessages.length > 0 ? this.filteredMessages : this.messages
      
      if (logsToExport.length === 0) {
        this.$message.warning('没有可导出的日志')
        return
      }
      
      this.$confirm(`确定要导出 ${logsToExport.length} 条日志记录吗？`, '导出确认', {
        confirmButtonText: '导出',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        const content = logsToExport.map(msg => 
          `[${msg.time}] ${msg.type.toUpperCase()}: ${msg.content}`
        ).join('\n')
        
        const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
        const url = URL.createObjectURL(blob)
        
        const a = document.createElement('a')
        a.href = url
        a.download = `mqtt_logs_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        
        this.$message.success('日志导出成功')
      })
    },
    
    clearMessages() {
      this.$confirm('确定要清空所有日志吗？此操作不可恢复。', '确认清空', {
        confirmButtonText: '清空',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$emit('clear-messages')
        this.filteredMessages = []
        this.messageTimestamps = []
      })
    },
    
    isValidJson(str) {
      try {
        JSON.parse(str)
        return true
      } catch (error) {
        return false
      }
    },
    
    formatJson() {
      if (!this.selectedMessage || !this.isValidJson(this.selectedMessage.content)) return
      
      try {
        const parsed = JSON.parse(this.selectedMessage.content)
        this.selectedMessage.content = JSON.stringify(parsed, null, 2)
      } catch (error) {
        this.$message.error('JSON格式化失败')
      }
    },
    
    formatSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    
    updateMessageTimestamps() {
      const now = Date.now()
      this.messageTimestamps.push(now)
      
      // 只保留最近1小时的时间戳
      const oneHourAgo = now - 3600000
      this.messageTimestamps = this.messageTimestamps.filter(timestamp => timestamp > oneHourAgo)
    }
  }
}
</script>

<style scoped>
.logger-card {
  margin-bottom: 20px;
}

.logger-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.message-count-badge {
  margin-right: 10px;
}

.filter-controls {
  margin-bottom: 15px;
}

.quick-filters {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-filter-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.quick-filter-tag:hover {
  transform: scale(1.05);
}

.filter-active {
  opacity: 0.7;
  border: 2px solid #409eff;
}

.messages-container {
  height: 400px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;
}

.message-item {
  padding: 10px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: background-color 0.2s;
}

.message-item:hover {
  background-color: #f0f9ff;
}

.message-item:last-child {
  border-bottom: none;
}

.message-item.system {
  border-left: 4px solid #909399;
}

.message-item.success {
  border-left: 4px solid #67c23a;
}

.message-item.error {
  border-left: 4px solid #f56c6c;
}

.message-item.warning {
  border-left: 4px solid #e6a23c;
}

.message-item.sent {
  border-left: 4px solid #409eff;
}

.message-item.received {
  border-left: 4px solid #67c23a;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.message-type-tag {
  font-size: 10px;
  padding: 2px 6px;
}

.message-time {
  font-size: 11px;
  color: #999;
}

.message-topic {
  font-size: 11px;
  color: #666;
  font-weight: 500;
}

.message-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.message-item:hover .message-actions {
  opacity: 1;
}

.message-content {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.4;
  word-break: break-all;
  color: #2c3e50;
}

.pause-indicator {
  text-align: center;
  padding: 20px;
  color: #e6a23c;
  background-color: #fdf6ec;
  border: 1px solid #f5dab1;
  margin: 10px;
  border-radius: 4px;
}

.pause-indicator i {
  margin-right: 8px;
  font-size: 16px;
}

.no-messages {
  text-align: center;
  color: #909399;
  padding: 40px 20px;
}

.no-messages i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.logger-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
  margin-top: 15px;
  font-size: 12px;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stats-label {
  color: #666;
}

.stats-value {
  font-weight: 500;
  color: #2c3e50;
}

.message-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.message-detail-content {
  margin: 15px 0;
}

.detail-actions {
  margin-top: 15px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .logger-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .message-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .message-actions {
    opacity: 1;
    align-self: flex-end;
  }
  
  .logger-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .quick-filters {
    justify-content: center;
  }
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 8px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>