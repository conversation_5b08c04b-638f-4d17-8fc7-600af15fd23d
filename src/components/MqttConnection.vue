<template>
  <el-card class="connection-card">
    <div slot="header" class="card-header">
      <span><i class="el-icon-connection"></i> MQTT连接配置</span>
      <connection-indicator :status="connectionStatus" />
    </div>
    
    <el-form 
      :model="config" 
      :rules="rules"
      ref="configForm"
      label-width="100px" 
      size="small">
      
      <!-- 连接方式 -->
      <el-form-item label="连接方式:" prop="protocol">
        <el-radio-group v-model="config.protocol">
          <el-radio label="ws">
            <span>WebSocket</span>
            <el-tooltip content="标准WebSocket连接，通常用于开发环境" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-radio>
          <el-radio label="wss">
            <span>WebSocket Secure</span>
            <el-tooltip content="加密WebSocket连接，推荐用于生产环境" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      
      <!-- 服务器地址 -->
      <el-form-item label="服务器:" prop="host">
        <el-input 
          v-model="config.host" 
          placeholder="例如: mqtt.example.com 或 localhost"
          :disabled="isConnecting || isConnected">
          <template slot="prepend">{{ config.protocol }}://</template>
        </el-input>
      </el-form-item>
      
      <!-- 端口和路径 -->
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="端口:" prop="port">
            <el-input-number 
              v-model="config.port" 
              :min="1" 
              :max="65535"
              :disabled="isConnecting || isConnected"
              style="width: 100%">
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路径:" prop="path">
            <el-input 
              v-model="config.path" 
              placeholder="/mqtt"
              :disabled="isConnecting || isConnected">
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 认证信息 -->
      <el-divider content-position="left">
        <span style="color: #909399; font-size: 12px;">认证信息 (可选)</span>
      </el-divider>
      
      <el-row :gutter="10">
        <el-col :span="12">
          <el-form-item label="用户名:">
            <el-input 
              v-model="config.username" 
              placeholder="可选"
              :disabled="isConnecting || isConnected">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密码:">
            <el-input 
              v-model="config.password" 
              type="password" 
              placeholder="可选"
              :disabled="isConnecting || isConnected"
              show-password>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 高级设置 -->
      <el-collapse v-model="advancedSettingsVisible">
        <el-collapse-item title="高级设置" name="advanced">
          <el-form-item label="客户端ID:">
            <el-input 
              v-model="config.clientId" 
              placeholder="自动生成"
              :disabled="isConnecting || isConnected">
              <el-button 
                slot="append" 
                @click="generateClientId"
                :disabled="isConnecting || isConnected">
                重新生成
              </el-button>
            </el-input>
          </el-form-item>
          
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="连接超时:">
                <el-input-number 
                  v-model="config.connectTimeout" 
                  :min="1000" 
                  :max="30000"
                  :step="1000"
                  :disabled="isConnecting || isConnected"
                  style="width: 100%">
                </el-input-number>
                <div class="form-item-tip">毫秒</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="重连间隔:">
                <el-input-number 
                  v-model="config.reconnectPeriod" 
                  :min="1000" 
                  :max="60000"
                  :step="1000"
                  :disabled="isConnecting || isConnected"
                  style="width: 100%">
                </el-input-number>
                <div class="form-item-tip">毫秒</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="心跳间隔:">
                <el-input-number 
                  v-model="config.keepalive" 
                  :min="10" 
                  :max="300"
                  :step="10"
                  :disabled="isConnecting || isConnected"
                  style="width: 100%">
                </el-input-number>
                <div class="form-item-tip">秒</div>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item>
            <el-checkbox v-model="config.clean" :disabled="isConnecting || isConnected">
              清除会话 (Clean Session)
            </el-checkbox>
          </el-form-item>
        </el-collapse-item>
      </el-collapse>
      
      <!-- 连接按钮 -->
      <el-form-item style="margin-top: 20px;">
        <el-button 
          type="primary" 
          @click="handleConnect" 
          :loading="isConnecting"
          :disabled="isConnected"
          style="width: 120px;">
          {{ isConnected ? '已连接' : (isConnecting ? '连接中...' : '连接') }}
        </el-button>
        
        <el-button 
          type="danger" 
          @click="handleDisconnect" 
          :disabled="!isConnected && !isConnecting"
          style="width: 80px;">
          断开
        </el-button>
        
        <el-button 
          @click="testConnection" 
          :disabled="isConnecting || isConnected"
          style="width: 80px;">
          测试
        </el-button>
      </el-form-item>
    </el-form>
    
    <!-- 连接信息 -->
    <el-divider content-position="left">
      <span style="color: #909399; font-size: 12px;">连接信息</span>
    </el-divider>
    
    <div class="connection-info">
      <div class="info-item">
        <span class="info-label">状态:</span>
        <el-tag :type="statusTagType" size="mini">{{ connectionStatusText }}</el-tag>
      </div>
      
      <div class="info-item" v-if="isConnected">
        <span class="info-label">客户端ID:</span>
        <span class="info-value">{{ mqttService?.client?.options?.clientId || 'N/A' }}</span>
      </div>
      
      <div class="info-item">
        <span class="info-label">连接URL:</span>
        <span class="info-value">{{ fullUrl }}</span>
      </div>
      
      <div class="info-item" v-if="lastError">
        <span class="info-label">错误:</span>
        <span class="info-value error">{{ lastError }}</span>
      </div>
    </div>
    
    <!-- 快速配置 -->
    <el-divider content-position="left">
      <span style="color: #909399; font-size: 12px;">快速配置</span>
    </el-divider>
    
    <div class="quick-configs">
      <el-button 
        size="mini" 
        @click="loadPreset('local')"
        :disabled="isConnecting || isConnected">
        本地测试
      </el-button>
      <el-button 
        size="mini" 
        @click="loadPreset('mosquitto')"
        :disabled="isConnecting || isConnected">
        Mosquitto
      </el-button>
      <el-button 
        size="mini" 
        @click="loadPreset('emqx')"
        :disabled="isConnecting || isConnected">
        EMQ X
      </el-button>
      <el-button 
        size="mini" 
        @click="saveConfig"
        :disabled="isConnecting">
        保存配置
      </el-button>
    </div>
  </el-card>
</template>

<script>
import MqttService from '../services/MqttService'
import ConnectionIndicator from './ConnectionIndicator.vue'

export default {
  name: 'MqttConnection',
  
  components: {
    ConnectionIndicator
  },
  
  data() {
    return {
      mqttService: null,
      isConnected: false,
      isConnecting: false,
      connectionStatus: 'disconnected', // disconnected, connecting, connected, error
      connectionStatusText: '未连接',
      lastError: '',
      advancedSettingsVisible: [],
      
      config: {
        protocol: 'ws',
        host: '**************',
        port: 38084,
        path: '/mqtt',
        username: '',
        password: '',
        clientId: this.generateRandomClientId(),
        connectTimeout: 4000,
        reconnectPeriod: 1000,
        keepalive: 60,
        clean: true
      },
      
      rules: {
        protocol: [
          { required: true, message: '请选择连接方式', trigger: 'change' }
        ],
        host: [
          { required: true, message: '请输入服务器地址', trigger: 'blur' },
          { pattern: /^[a-zA-Z0-9.-]+$/, message: '服务器地址格式不正确', trigger: 'blur' }
        ],
        port: [
          { required: true, message: '请输入端口号', trigger: 'blur' },
          { type: 'number', min: 1, max: 65535, message: '端口号必须在1-65535之间', trigger: 'blur' }
        ]
      }
    }
  },
  
  computed: {
    statusTagType() {
      switch (this.connectionStatus) {
        case 'connected': return 'success'
        case 'connecting': return 'warning'
        case 'error': return 'danger'
        default: return 'info'
      }
    },
    
    fullUrl() {
      return `${this.config.protocol}://${this.config.host}:${this.config.port}${this.config.path}`
    }
  },
  
  mounted() {
    // 加载保存的配置
    this.loadSavedConfig()
  },
  
  methods: {
    async handleConnect() {
      // 表单验证
      try {
        await this.$refs.configForm.validate()
      } catch (error) {
        this.$message.error('请检查配置信息')
        return
      }
      
      this.isConnecting = true
      this.connectionStatus = 'connecting'
      this.connectionStatusText = '连接中...'
      this.lastError = ''
      
      console.log('=== 开始连接MQTT ===')
      console.log('配置信息:', this.config)
      console.log('完整URL:', this.fullUrl)
      
      this.$emit('message-log', 'system', `尝试连接到: ${this.fullUrl}`)
      
      try {
        this.mqttService = new MqttService()
        
        // 设置事件监听
        this.mqttService.on('connect', () => {
          console.log('✅ 连接事件触发')
          this.isConnected = true
          this.isConnecting = false
          this.connectionStatus = 'connected'
          this.connectionStatusText = '已连接'
          this.$message.success('连接成功')
          this.$emit('message-log', 'success', '连接成功')
          this.$emit('connection-change', true, this.mqttService)
        })
        
        this.mqttService.on('error', (err) => {
          console.error('❌ 连接错误事件:', err)
          this.isConnecting = false
          this.isConnected = false
          this.connectionStatus = 'error'
          this.connectionStatusText = '连接失败'
          this.lastError = err.message
          this.$message.error(`连接失败: ${err.message}`)
          this.$emit('message-log', 'error', `连接失败: ${err.message}`)
          
          // 详细错误分析
          this.analyzeError(err)
        })
        
        this.mqttService.on('disconnect', () => {
          console.log('🔴 断开连接事件')
          this.isConnected = false
          this.connectionStatus = 'disconnected'
          this.connectionStatusText = '连接已断开'
          this.$message.warning('连接已断开')
          this.$emit('message-log', 'warning', '连接已断开')
          this.$emit('connection-change', false, null)
        })
        
        this.mqttService.on('reconnect', () => {
          console.log('🔄 重连事件')
          this.connectionStatus = 'connecting'
          this.connectionStatusText = '重连中...'
          this.$emit('message-log', 'warning', '正在重连...')
        })
        
        await this.mqttService.connect(this.config)
        
      } catch (error) {
        console.error('❌ 连接异常:', error)
        this.isConnecting = false
        this.connectionStatus = 'error'
        this.connectionStatusText = '连接失败'
        this.lastError = error.message
        this.$message.error(`连接失败: ${error.message}`)
        this.$emit('message-log', 'error', `连接失败: ${error.message}`)
        
        // 详细错误分析
        this.analyzeError(error)
      }
    },
    
    // 新增错误分析方法
    analyzeError(error) {
      console.log('=== 错误分析 ===')
      console.log('错误类型:', error.constructor.name)
      console.log('错误消息:', error.message)
      console.log('错误代码:', error.code)
      console.log('错误详情:', error)
      
      let suggestion = ''
      
      if (error.message.includes('ECONNREFUSED')) {
        suggestion = '连接被拒绝，请检查：\n1. MQTT服务器是否启动\n2. 端口是否正确\n3. 防火墙设置'
      } else if (error.message.includes('ENOTFOUND')) {
        suggestion = '域名解析失败，请检查：\n1. 服务器地址是否正确\n2. 网络连接是否正常'
      } else if (error.message.includes('timeout')) {
        suggestion = '连接超时，请检查：\n1. 网络是否稳定\n2. 服务器是否响应\n3. 增加连接超时时间'
      } else if (error.message.includes('WebSocket')) {
        suggestion = 'WebSocket连接问题，请检查：\n1. 服务器是否支持WebSocket\n2. 路径是否正确\n3. 协议是否匹配'
      } else {
        suggestion = '未知错误，请检查控制台详细信息'
      }
      
      console.log('建议解决方案:', suggestion)
      this.$emit('message-log', 'error', `错误分析: ${suggestion}`)
    },
    
    handleDisconnect() {
      if (this.mqttService) {
        this.mqttService.disconnect()
        this.mqttService = null
      }
      
      this.isConnected = false
      this.isConnecting = false
      this.connectionStatus = 'disconnected'
      this.connectionStatusText = '已断开'
      this.lastError = ''
      
      this.$message.info('已断开连接')
      this.$emit('message-log', 'system', '主动断开连接')
      this.$emit('connection-change', false, null)
    },
    
    async testConnection() {
      this.$emit('message-log', 'system', '开始连接测试...')
      
      try {
        const testService = new MqttService()
        await testService.connect({
          ...this.config,
          clientId: `test_${Date.now()}`
        })
        
        testService.disconnect()
        this.$message.success('连接测试成功')
        this.$emit('message-log', 'success', '连接测试成功')
        
      } catch (error) {
        this.$message.error(`连接测试失败: ${error.message}`)
        this.$emit('message-log', 'error', `连接测试失败: ${error.message}`)
      }
    },
    
    generateClientId() {
      this.config.clientId = this.generateRandomClientId()
    },
    
    generateRandomClientId() {
      return `vue_client_${Math.random().toString(16).substr(2, 8)}_${Date.now()}`
    },
    
    loadPreset(type) {
      const presets = {
        local: {
          protocol: 'ws',
          host: 'localhost',
          port: 8083,
          path: '/mqtt',
          username: '',
          password: ''
        },
        mosquitto: {
          protocol: 'ws',
          host: 'test.mosquitto.org',
          port: 8080,
          path: '',
          username: '',
          password: ''
        },
        emqx: {
          protocol: 'ws',
          host: 'broker.emqx.io',
          port: 8083,
          path: '/mqtt',
          username: '',
          password: ''
        }
      }
      
      if (presets[type]) {
        Object.assign(this.config, presets[type])
        this.generateClientId()
        this.$message.success(`已加载${type === 'local' ? '本地' : type.toUpperCase()}配置`)
      }
    },
    
    saveConfig() {
      try {
        const configToSave = { ...this.config }
        delete configToSave.password // 不保存密码
        localStorage.setItem('mqtt_config', JSON.stringify(configToSave))
        this.$message.success('配置已保存')
      } catch (error) {
        this.$message.error('保存配置失败')
      }
    },
    
    loadSavedConfig() {
      try {
        const saved = localStorage.getItem('mqtt_config')
        if (saved) {
          const config = JSON.parse(saved)
          Object.assign(this.config, config)
          this.generateClientId() // 重新生成客户端ID
        }
      } catch (error) {
        console.error('加载保存的配置失败:', error)
      }
    }
  },
  
  beforeDestroy() {
    if (this.mqttService && this.isConnected) {
      this.mqttService.disconnect()
    }
  }
}
</script>

<style scoped>
.connection-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.connection-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  min-width: 80px;
  color: #666;
  font-size: 13px;
}

.info-value {
  flex: 1;
  font-size: 13px;
  word-break: break-all;
}

.info-value.error {
  color: #f56c6c;
}

.form-item-tip {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.quick-configs {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.el-divider {
  margin: 15px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .quick-configs {
    justify-content: center;
  }
  
  .info-item {
    flex-direction: column;
  }
  
  .info-label {
    min-width: auto;
    margin-bottom: 2px;
  }
}
</style>