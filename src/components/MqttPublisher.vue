<template>
  <el-card class="publisher-card">
    <div slot="header">
      <span><i class="el-icon-upload"></i> 消息发布</span>
    </div>
    
    <el-form 
      :model="publishForm" 
      :rules="publishRules"
      ref="publishForm"
      label-width="80px" 
      size="small">
      
      <!-- 主题输入 -->
      <el-form-item label="主题:" prop="topic">
        <el-input 
          v-model="publishForm.topic" 
          placeholder="例如: sensor/temperature"
          @keyup.enter.native="publishMessage">
          <el-dropdown slot="append" @command="selectTopic">
            <el-button icon="el-icon-arrow-down"></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item 
                v-for="topic in recentTopics" 
                :key="topic"
                :command="topic">
                {{ topic }}
              </el-dropdown-item>
              <el-dropdown-item divided command="clear">
                <span style="color: #f56c6c;">清空历史</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-input>
      </el-form-item>
      
      <!-- 消息内容 -->
      <el-form-item label="消息:" prop="message">
        <el-input 
          type="textarea" 
          v-model="publishForm.message" 
          :rows="4"
          placeholder="输入要发送的消息内容..."
          @keyup.ctrl.enter.native="publishMessage"
          show-word-limit
          :maxlength="1000">
        </el-input>
        <div class="message-tips">
          <el-button-group size="mini">
            <el-button @click="insertTemplate('json')">JSON</el-button>
            <el-button @click="insertTemplate('timestamp')">时间戳</el-button>
            <el-button @click="insertTemplate('random')">随机数</el-button>
            <el-button @click="formatMessage">格式化</el-button>
          </el-button-group>
        </div>
      </el-form-item>
      
      <!-- 发布选项 -->
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="QoS:">
            <el-select v-model="publishForm.qos" style="width: 100%">
              <el-option 
                label="0 - 最多一次" 
                :value="0"
                title="消息最多传递一次，可能丢失">
              </el-option>
              <el-option 
                label="1 - 至少一次" 
                :value="1"
                title="消息至少传递一次，可能重复">
              </el-option>
              <el-option 
                label="2 - 恰好一次" 
                :value="2"
                title="消息恰好传递一次，最高可靠性">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="保留:">
            <el-switch 
              v-model="publishForm.retain"
              active-text="是"
              inactive-text="否">
            </el-switch>
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="重复:">
            <el-switch 
              v-model="publishForm.dup"
              active-text="是"
              inactive-text="否">
            </el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      
      <!-- 发布按钮 -->
      <el-form-item>
        <el-button 
          type="primary" 
          @click="publishMessage"
          :loading="isPublishing"
          icon="el-icon-upload">
          发布消息
        </el-button>
        
        <el-button 
          @click="clearForm"
          icon="el-icon-refresh-left">
          清空
        </el-button>
        
        <el-button 
          @click="saveTemplate"
          icon="el-icon-document-add">
          保存模板
        </el-button>
      </el-form-item>
    </el-form>
    
    <!-- 快速发布模板 -->
    <el-divider content-position="left">
      <span style="color: #909399; font-size: 12px;">快速模板</span>
    </el-divider>
    
    <div class="templates-section">
      <div 
        v-for="template in templates" 
        :key="template.id"
        class="template-item">
        <div class="template-header">
          <span class="template-name">{{ template.name }}</span>
          <div class="template-actions">
            <el-button 
              size="mini" 
              type="text" 
              @click="loadTemplate(template)"
              icon="el-icon-download">
              使用
            </el-button>
            <el-button 
              size="mini" 
              type="text" 
              @click="deleteTemplate(template.id)"
              icon="el-icon-delete"
              style="color: #f56c6c;">
              删除
            </el-button>
          </div>
        </div>
        <div class="template-content">
          <div class="template-topic">主题: {{ template.topic }}</div>
          <div class="template-message">{{ template.message.substring(0, 50) }}{{ template.message.length > 50 ? '...' : '' }}</div>
        </div>
      </div>
      
      <div v-if="templates.length === 0" class="no-templates">
        暂无保存的模板
      </div>
    </div>
    
    <!-- 发布统计 -->
    <el-divider content-position="left">
      <span style="color: #909399; font-size: 12px;">发布统计</span>
    </el-divider>
    
    <div class="publish-stats">
      <div class="stat-item">
        <span class="stat-label">成功:</span>
        <span class="stat-value success">{{ publishStats.success }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">失败:</span>
        <span class="stat-value error">{{ publishStats.error }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">总计:</span>
        <span class="stat-value">{{ publishStats.total }}</span>
      </div>
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'MqttPublisher',
  
  props: {
    mqttService: {
      type: Object,
      required: true
    }
  },
  
  data() {
    return {
      isPublishing: false,
      
      publishForm: {
        topic: 'test/topic',
        message: '{"message": "Hello MQTT!", "timestamp": ' + Date.now() + '}',
        qos: 0,
        retain: false,
        dup: false
      },
      
      publishRules: {
        topic: [
          { required: true, message: '请输入主题', trigger: 'blur' },
          { min: 1, max: 200, message: '主题长度在 1 到 200 个字符', trigger: 'blur' }
        ],
        message: [
          { required: true, message: '请输入消息内容', trigger: 'blur' }
        ]
      },
      
      recentTopics: [],
      templates: [],
      publishStats: {
        success: 0,
        error: 0,
        total: 0
      }
    }
  },
  
  mounted() {
    this.loadRecentTopics()
    this.loadTemplates()
    this.loadPublishStats()
  },
  
  methods: {
    async publishMessage() {
      // 表单验证
      try {
        await this.$refs.publishForm.validate()
      } catch (error) {
        return
      }
      
      this.isPublishing = true
      
      try {
        await this.mqttService.publish(
          this.publishForm.topic,
          this.publishForm.message,
          {
            qos: this.publishForm.qos,
            retain: this.publishForm.retain,
            dup: this.publishForm.dup
          }
        )
        
        this.$message.success('消息发布成功')
        this.$emit('message-log', 'sent', `[${this.publishForm.topic}] ${this.publishForm.message}`, this.publishForm.topic)
        
        // 更新统计
        this.publishStats.success++
        this.publishStats.total++
        this.savePublishStats()
        
        // 保存到最近使用的主题
        this.addRecentTopic(this.publishForm.topic)
        
      } catch (error) {
        this.$message.error(`发布失败: ${error.message}`)
        this.$emit('message-log', 'error', `发布失败: ${error.message}`)
        
        // 更新统计
        this.publishStats.error++
        this.publishStats.total++
        this.savePublishStats()
      }
      
      this.isPublishing = false
    },
    
    clearForm() {
      this.publishForm.topic = ''
      this.publishForm.message = ''
      this.publishForm.qos = 0
      this.publishForm.retain = false
      this.publishForm.dup = false
    },
    
    insertTemplate(type) {
      let template = ''
      
      switch (type) {
        case 'json':
          template = '{\n  "message": "Hello MQTT!",\n  "timestamp": ' + Date.now() + ',\n  "value": 0\n}'
          break
        case 'timestamp':
          template = Date.now().toString()
          break
        case 'random':
          template = Math.random().toString()
          break
        default:
          return
      }
      
      // 插入模板到当前光标位置
      if (this.publishForm.message) {
        this.publishForm.message += '\n' + template
      } else {
        this.publishForm.message = template
      }
    },
    
    formatMessage() {
      try {
        const parsed = JSON.parse(this.publishForm.message)
        this.publishForm.message = JSON.stringify(parsed, null, 2)
        this.$message.success('JSON格式化成功')
      } catch (error) {
        this.$message.warning('消息内容不是有效的JSON格式')
      }
    },
    
    saveTemplate() {
      if (!this.publishForm.topic || !this.publishForm.message) {
        this.$message.error('请先填写主题和消息内容')
        return
      }
      
      this.$prompt('请输入模板名称', '保存模板', {
        confirmButtonText: '保存',
        cancelButtonText: '取消',
        inputPattern: /^.{1,20}$/,
        inputErrorMessage: '模板名称长度在1-20个字符'
      }).then(({ value }) => {
        const template = {
          id: Date.now(),
          name: value,
          topic: this.publishForm.topic,
          message: this.publishForm.message,
          qos: this.publishForm.qos,
          retain: this.publishForm.retain,
          dup: this.publishForm.dup,
          createTime: new Date().toLocaleString()
        }
        
        this.templates.push(template)
        this.saveTemplates()
        this.$message.success('模板保存成功')
      }).catch(() => {
        // 用户取消
      })
    },
    
    loadTemplate(template) {
      this.publishForm.topic = template.topic
      this.publishForm.message = template.message
      this.publishForm.qos = template.qos
      this.publishForm.retain = template.retain
      this.publishForm.dup = template.dup
      this.$message.success(`已加载模板: ${template.name}`)
    },
    
    deleteTemplate(templateId) {
      this.$confirm('确定要删除这个模板吗？', '确认删除', {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.templates = this.templates.filter(t => t.id !== templateId)
        this.saveTemplates()
        this.$message.success('模板删除成功')
      }).catch(() => {
        // 用户取消
      })
    },
    
    selectTopic(topic) {
      if (topic === 'clear') {
        this.recentTopics = []
        this.saveRecentTopics()
        this.$message.success('历史主题已清空')
      } else {
        this.publishForm.topic = topic
      }
    },
    
    addRecentTopic(topic) {
      // 移除重复项
      this.recentTopics = this.recentTopics.filter(t => t !== topic)
      // 添加到开头
      this.recentTopics.unshift(topic)
      // 限制数量
      if (this.recentTopics.length > 10) {
        this.recentTopics = this.recentTopics.slice(0, 10)
      }
      this.saveRecentTopics()
    },
    
    saveRecentTopics() {
      try {
        localStorage.setItem('mqtt_recent_topics', JSON.stringify(this.recentTopics))
      } catch (error) {
        console.error('保存最近主题失败:', error)
      }
    },
    
    loadRecentTopics() {
      try {
        const saved = localStorage.getItem('mqtt_recent_topics')
        if (saved) {
          this.recentTopics = JSON.parse(saved)
        }
      } catch (error) {
        console.error('加载最近主题失败:', error)
        this.recentTopics = []
      }
    },
    
    saveTemplates() {
      try {
        localStorage.setItem('mqtt_templates', JSON.stringify(this.templates))
      } catch (error) {
        console.error('保存模板失败:', error)
      }
    },
    
    loadTemplates() {
      try {
        const saved = localStorage.getItem('mqtt_templates')
        if (saved) {
          this.templates = JSON.parse(saved)
        }
      } catch (error) {
        console.error('加载模板失败:', error)
        this.templates = []
      }
    },
    
    savePublishStats() {
      try {
        localStorage.setItem('mqtt_publish_stats', JSON.stringify(this.publishStats))
      } catch (error) {
        console.error('保存发布统计失败:', error)
      }
    },
    
    loadPublishStats() {
      try {
        const saved = localStorage.getItem('mqtt_publish_stats')
        if (saved) {
          this.publishStats = JSON.parse(saved)
        }
      } catch (error) {
        console.error('加载发布统计失败:', error)
        this.publishStats = { success: 0, error: 0, total: 0 }
      }
    }
  }
}
</script>

<style scoped>
.publisher-card {
  margin-bottom: 20px;
}

.message-tips {
  margin-top: 8px;
  display: flex;
  justify-content: flex-end;
}

.templates-section {
  max-height: 200px;
  overflow-y: auto;
}

.template-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  padding: 10px;
  background-color: #fafafa;
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.template-name {
  font-weight: 500;
  color: #303133;
}

.template-actions {
  display: flex;
  gap: 5px;
}

.template-content {
  font-size: 12px;
  color: #666;
}

.template-topic {
  margin-bottom: 3px;
  font-weight: 500;
}

.template-message {
  line-height: 1.4;
  word-break: break-all;
}

.no-templates {
  text-align: center;
  color: #909399;
  padding: 20px;
  font-size: 14px;
}

.publish-stats {
  display: flex;
  gap: 20px;
  align-items: center;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-label {
  font-size: 13px;
  color: #666;
}

.stat-value {
  font-size: 14px;
  font-weight: 500;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.error {
  color: #f56c6c;
}

.el-divider {
  margin: 15px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .template-actions {
    align-self: flex-end;
  }
  
  .publish-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .message-tips {
    justify-content: center;
  }
}
</style>
          