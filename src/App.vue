<template>
  <div id="app">
    <el-container class="app-container">
      <!-- 头部 -->
      <el-header class="app-header">
        <div class="header-content">
          <h1 class="app-title">
            <i class="el-icon-connection"></i>
            {{ $appName }}
          </h1>
          <div class="header-info">
            <el-tag size="mini" type="info">v{{ $version }}</el-tag>
            <connection-status :is-connected="mqttService.isConnected" />
          </div>
        </div>
      </el-header>
      
      <!-- 主体内容 -->
      <el-main class="app-main">
        <el-row :gutter="20">
          <!-- 左侧：连接和配置 -->
          <el-col :xs="24" :sm="24" :md="12" :lg="8">
            <mqtt-connection 
              ref="mqttConnection"
              @connection-change="handleConnectionChange"
              @message-log="addMessageLog"
            />
            
            <mqtt-subscription 
              v-if="mqttService.isConnected"
              :mqtt-service="mqttService"
              @message-log="addMessageLog"
            />
          </el-col>
          
          <!-- 右侧：消息和日志 -->
          <el-col :xs="24" :sm="24" :md="12" :lg="16">
            <mqtt-publisher 
              v-if="mqttService.isConnected"
              :mqtt-service="mqttService"
              @message-log="addMessageLog"
            />
            
            <message-logger 
              :messages="messageLogs"
              @clear-messages="clearMessageLogs"
            />
          </el-col>
        </el-row>
      </el-main>
      
      <!-- 底部 -->
      <el-footer class="app-footer">
        <div class="footer-content">
          <span>© 2025 MQTT测试工具 | Vue 2 + Element UI</span>
          <span>
            <a href="https://github.com/mqttjs/MQTT.js" target="_blank">MQTT.js</a>
            |
            <a href="https://element.eleme.io/" target="_blank">Element UI</a>
          </span>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import MqttConnection from './components/MqttConnection.vue'
import MqttPublisher from './components/MqttPublisher.vue'
import MqttSubscription from './components/MqttSubscription.vue'
import MessageLogger from './components/MessageLogger.vue'
import ConnectionStatus from './components/ConnectionStatus.vue'
import MqttService from './services/MqttService'

export default {
  name: 'App',
  
  components: {
    MqttConnection,
    MqttPublisher,
    MqttSubscription,
    MessageLogger,
    ConnectionStatus
  },
  
  data() {
    return {
      mqttService: new MqttService(),
      messageLogs: []
    }
  },
  
  methods: {
    handleConnectionChange(isConnected, service) {
      if (isConnected) {
        this.mqttService = service
        // 监听接收到的消息
        this.mqttService.onMessage((topic, message) => {
          this.addMessageLog('received', `[${topic}] ${message.toString()}`)
        })
      } else {
        this.mqttService = new MqttService()
      }
    },
    
    addMessageLog(type, content, topic = null) {
      this.messageLogs.unshift({
        id: Date.now() + Math.random(),
        type,
        content,
        topic,
        time: new Date().toLocaleTimeString(),
        timestamp: Date.now()
      })
      
      // 限制日志数量
      if (this.messageLogs.length > 200) {
        this.messageLogs = this.messageLogs.slice(0, 200)
      }
    },
    
    clearMessageLogs() {
      this.messageLogs = []
      this.$message.success('日志已清空')
    }
  },
  
  beforeDestroy() {
    if (this.mqttService && this.mqttService.isConnected) {
      this.mqttService.disconnect()
    }
  }
}
</script>

<style scoped>
.app-container {
  min-height: 100vh;
}

.app-header {
  background: linear-gradient(135deg, #409EFF 0%, #66b3ff 100%);
  color: white;
  padding: 0 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.app-title {
  margin: 0;
  font-size: 24px;
  font-weight: 300;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.app-main {
  background-color: #f5f7fa;
  padding: 20px;
  min-height: calc(100vh - 120px);
}

.app-footer {
  background-color: #2c3e50;
  color: #ecf0f1;
  padding: 0 20px;
  height: 60px !important;
  line-height: 60px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.footer-content a {
  color: #3498db;
  text-decoration: none;
  margin: 0 5px;
}

.footer-content a:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .app-title {
    font-size: 18px;
  }
  
  .header-content {
    flex-direction: column;
    justify-content: center;
    gap: 10px;
  }
  
  .footer-content {
    flex-direction: column;
    line-height: 1.5;
    text-align: center;
  }
}
</style>