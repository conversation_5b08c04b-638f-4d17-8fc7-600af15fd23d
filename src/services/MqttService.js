import mqtt from 'mqtt'

/**
 * MQTT服务类
 * 封装MQTT连接和操作逻辑
 */
class MqttService {
  constructor() {
    this.client = null
    this.isConnected = false
    this.isConnecting = false
    this.subscriptions = new Map() // topic -> { qos, callback }
    this.messageHandlers = []
    this.eventHandlers = {
      connect: [],
      disconnect: [],
      error: [],
      reconnect: []
    }
  }

  /**
   * 连接到MQTT服务器
   * @param {Object} config 连接配置
   * @returns {Promise}
   */
  connect(config) {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || this.isConnected) {
        reject(new Error('已经连接或正在连接中'))
        return
      }

      this.isConnecting = true

      const options = {
        clientId: config.clientId || `vue_client_${Math.random().toString(16).substr(2, 8)}`,
        clean: config.clean !== false, // 默认为true
        connectTimeout: config.connectTimeout || 4000,
        reconnectPeriod: config.reconnectPeriod || 1000,
        keepalive: config.keepalive || 60
      }

      // 添加认证信息
      if (config.username) {
        options.username = config.username
      }
      if (config.password) {
        options.password = config.password
      }

      // 构建连接URL
      const url = `${config.protocol}://${config.host}:${config.port}${config.path || ''}`
      
      // 详细日志
      console.log('=== MQTT连接调试信息 ===')
      console.log('连接URL:', url)
      console.log('连接选项:', options)
      console.log('配置信息:', config)

      try {
        this.client = mqtt.connect(url, options)

        // 连接成功
        this.client.on('connect', () => {
          console.log('✅ MQTT连接成功')
          console.log('连接信息:', this.client.options)
          this.isConnected = true
          this.isConnecting = false
          this._triggerEvent('connect')
          resolve(this)
        })

        // 连接错误
        this.client.on('error', (err) => {
          console.error('❌ MQTT连接错误:', err)
          console.error('错误详情:', {
            message: err.message,
            code: err.code,
            errno: err.errno,
            stack: err.stack
          })
          this.isConnecting = false
          this.isConnected = false
          this._triggerEvent('error', err)
          reject(err)
        })

        // 连接关闭
        this.client.on('close', () => {
          console.log('🔴 MQTT连接关闭')
          this.isConnected = false
          this._triggerEvent('disconnect')
        })

        // 重连
        this.client.on('reconnect', () => {
          console.log('🔄 MQTT重连中...')
          this._triggerEvent('reconnect')
        })

        // 离线
        this.client.on('offline', () => {
          console.log('📡 MQTT离线')
        })

        // 接收消息
        this.client.on('message', (topic, message, packet) => {
          this._handleMessage(topic, message, packet)
        })

      } catch (error) {
        console.error('❌ 创建MQTT客户端失败:', error)
        this.isConnecting = false
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.client) {
      this.client.end()
      this.client = null
    }
    this.isConnected = false
    this.isConnecting = false
    this.subscriptions.clear()
  }

  /**
   * 发布消息
   * @param {string} topic 主题
   * @param {string|Buffer} message 消息内容
   * @param {Object} options 发布选项
   * @returns {Promise}
   */
  publish(topic, message, options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected || !this.client) {
        reject(new Error('MQTT未连接'))
        return
      }

      const publishOptions = {
        qos: options.qos || 0,
        retain: options.retain || false,
        dup: options.dup || false
      }

      this.client.publish(topic, message, publishOptions, (err) => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      })
    })
  }

  /**
   * 订阅主题
   * @param {string} topic 主题
   * @param {Object} options 订阅选项
   * @returns {Promise}
   */
  subscribe(topic, options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected || !this.client) {
        reject(new Error('MQTT未连接'))
        return
      }

      const subscribeOptions = {
        qos: options.qos || 0
      }

      this.client.subscribe(topic, subscribeOptions, (err, granted) => {
        if (err) {
          reject(err)
        } else {
          // 保存订阅信息
          this.subscriptions.set(topic, {
            qos: subscribeOptions.qos,
            granted: granted
          })
          resolve(granted)
        }
      })
    })
  }

  /**
   * 取消订阅
   * @param {string} topic 主题
   * @returns {Promise}
   */
  unsubscribe(topic) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected || !this.client) {
        reject(new Error('MQTT未连接'))
        return
      }

      this.client.unsubscribe(topic, (err) => {
        if (err) {
          reject(err)
        } else {
          this.subscriptions.delete(topic)
          resolve()
        }
      })
    })
  }

  /**
   * 获取订阅列表
   * @returns {Array}
   */
  getSubscriptions() {
    return Array.from(this.subscriptions.entries()).map(([topic, info]) => ({
      topic,
      qos: info.qos
    }))
  }

  /**
   * 添加消息处理器
   * @param {Function} handler 处理函数 (topic, message, packet) => {}
   */
  onMessage(handler) {
    if (typeof handler === 'function') {
      this.messageHandlers.push(handler)
    }
  }

  /**
   * 移除消息处理器
   * @param {Function} handler 处理函数
   */
  offMessage(handler) {
    const index = this.messageHandlers.indexOf(handler)
    if (index > -1) {
      this.messageHandlers.splice(index, 1)
    }
  }

  /**
   * 添加事件监听器
   * @param {string} event 事件名称 (connect, disconnect, error, reconnect)
   * @param {Function} handler 处理函数
   */
  on(event, handler) {
    if (this.eventHandlers[event] && typeof handler === 'function') {
      this.eventHandlers[event].push(handler)
    }
  }

  /**
   * 移除事件监听器
   * @param {string} event 事件名称
   * @param {Function} handler 处理函数
   */
  off(event, handler) {
    if (this.eventHandlers[event]) {
      const index = this.eventHandlers[event].indexOf(handler)
      if (index > -1) {
        this.eventHandlers[event].splice(index, 1)
      }
    }
  }

  /**
   * 获取连接状态信息
   * @returns {Object}
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      subscriptionsCount: this.subscriptions.size,
      clientId: this.client ? this.client.options.clientId : null
    }
  }

  /**
   * 处理接收到的消息
   * @private
   */
  _handleMessage(topic, message, packet) {
    // 触发所有消息处理器
    this.messageHandlers.forEach(handler => {
      try {
        handler(topic, message, packet)
      } catch (error) {
        console.error('消息处理器错误:', error)
      }
    })
  }

  /**
   * 触发事件
   * @private
   */
  _triggerEvent(event, ...args) {
    if (this.eventHandlers[event]) {
      this.eventHandlers[event].forEach(handler => {
        try {
          handler(...args)
        } catch (error) {
          console.error('事件处理器错误:', error)
        }
      })
    }
  }
}

export default MqttService