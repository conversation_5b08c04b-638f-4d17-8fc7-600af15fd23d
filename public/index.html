<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title>Vue2 MQTT测试工具</title>
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
      }
      
      #loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f5f5f5;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #409EFF;
        border-top: 3px solid transparent;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong>很抱歉，这个应用需要JavaScript支持才能正常运行。请启用JavaScript后重新访问。</strong>
    </noscript>
    
    <div id="loading">
      <div>
        <div class="loading-spinner"></div>
        <p style="margin-top: 20px; color: #666;">正在加载...</p>
      </div>
    </div>
    
    <div id="app"></div>
    
    <script>
      // 隐藏加载动画
      window.addEventListener('load', function() {
        setTimeout(function() {
          var loading = document.getElementById('loading');
          if (loading) {
            loading.style.display = 'none';
          }
        }, 500);
      });
    </script>
  </body>
</html>