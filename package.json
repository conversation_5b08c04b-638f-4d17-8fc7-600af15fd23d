{"name": "vue2-mqtt-test", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"core-js": "^3.8.3", "element-ui": "^2.15.14", "mqtt": "^4.3.7", "vue": "^2.6.14"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "buffer": "^6.0.3", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "events": "^3.3.0", "path-browserify": "^1.0.1", "process": "^0.11.10", "stream": "^0.0.3", "stream-browserify": "^3.0.0", "url": "^0.11.4", "util": "^0.12.5", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}