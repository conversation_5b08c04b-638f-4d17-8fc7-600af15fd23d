<template>
  <div class="connection-status">
    <div class="status-indicator">
      <div :class="['status-dot', statusClass]"></div>
      <span class="status-text">{{ statusText }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConnectionStatus',
  
  props: {
    isConnected: {
      type: Boolean,
      default: false
    }
  },
  
  computed: {
    statusClass() {
      return this.isConnected ? 'connected' : 'disconnected'
    },
    
    statusText() {
      return this.isConnected ? '在线' : '离线'
    }
  }
}
</script>

<style scoped>
.connection-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
}

.status-dot.connected {
  background-color: #67c23a;
  animation: pulse 2s infinite;
}

.status-dot.disconnected {
  background-color: #f56c6c;
}

.status-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}
</style>