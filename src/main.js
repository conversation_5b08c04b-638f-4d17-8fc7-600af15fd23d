import Vue from 'vue'
import App from './App.vue'

// 引入Element UI
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

// 引入全局样式
import './styles/global.css'

// 使用Element UI
Vue.use(ElementUI)

Vue.config.productionTip = false

// 全局错误处理
Vue.config.errorHandler = function (err, vm, info) {
  console.error('Vue Error:', err)
  console.error('Component:', vm)
  console.error('Info:', info)
}

// 全局属性
Vue.prototype.$appName = 'MQTT测试工具'
Vue.prototype.$version = '1.0.0'

new Vue({
  render: h => h(App),
}).$mount('#app')