# Vue 2 MQTT 测试工具

一个功能完整的Vue 2 MQTT客户端测试工具，支持WebSocket和WebSocket Secure连接，具备完整的消息发布、订阅和日志管理功能。

## 🎯 功能特性

### 🔗 连接管理
- ✅ 支持 `ws://` 和 `wss://` 连接方式
- ✅ 完整的连接配置（服务器、端口、路径、认证等）
- ✅ 连接状态实时监控
- ✅ 自动重连机制
- ✅ 连接测试功能
- ✅ 快速配置预设

### 📤 消息发布
- ✅ 支持各种QoS级别（0, 1, 2）
- ✅ 保留消息和重复消息选项
- ✅ 消息模板系统
- ✅ JSON格式化工具
- ✅ 发布统计
- ✅ 历史主题记录

### 📥 订阅管理
- ✅ 支持MQTT通配符（+ 和 #）
- ✅ 多主题订阅管理
- ✅ 消息过滤器
- ✅ 订阅统计
- ✅ 实时消息计数

### 📊 日志系统
- ✅ 实时消息日志
- ✅ 消息类型过滤
- ✅ 内容搜索功能
- ✅ 日志导出（TXT/JSON）
- ✅ 消息详情查看
- ✅ 自动滚动和暂停功能

### 🎨 用户界面
- ✅ 现代化响应式设计
- ✅ Element UI组件库
- ✅ 实时连接状态指示
- ✅ 直观的操作界面
- ✅ 移动端适配

## 🚀 快速开始

### 环境要求
- Node.js 14.0+
- npm 或 yarn

### 1. 创建项目
```bash
# 使用Vue CLI创建项目
vue create vue2-mqtt-test
cd vue2-mqtt-test
```

### 2. 安装依赖
```bash
# 安装项目依赖
npm install

# 安装MQTT和UI库
npm install mqtt@4.3.7 element-ui@2.15.14

# 如果需要 buffer polyfill（某些环境下需要）
npm install buffer process --save-dev
```

### 3. 复制项目文件
将工程中的各个文件复制到对应的位置：

```
src/
├── components/
│   ├── ConnectionIndicator.vue
│   ├── ConnectionStatus.vue
│   ├── MessageLogger.vue
│   ├── MqttConnection.vue
│   ├── MqttPublisher.vue
│   └── MqttSubscription.vue
├── services/
│   └── MqttService.js
├── styles/
│   └── global.css
├── App.vue
└── main.js

public/
└── index.html

# 配置文件
vue.config.js
babel.config.js
package.json
```

### 4. 配置MQTT服务器
编辑 `vue.config.js` 文件，修改代理配置：

```javascript
proxy: {
  '/mqtt': {
    target: 'ws://your-mqtt-server.com:8083', // 替换为你的MQTT服务器地址
    ws: true,
    changeOrigin: true,
    pathRewrite: {
      '^/mqtt': ''
    }
  }
}
```

### 5. 启动项目
```bash
npm run serve
```

访问 http://localhost:8080 即可使用。

## 🔧 MQTT服务器配置

### Mosquitto配置示例
```bash
# mosquitto.conf
listener 1883
protocol mqtt

# WebSocket支持
listener 8083
protocol websockets
allow_anonymous true

# SSL WebSocket支持（可选）
listener 8084
protocol websockets
cafile /path/to/ca.crt
certfile /path/to/server.crt
keyfile /path/to/server.key
```

### EMQ X配置示例
```bash
# emqx.conf
web.socket.port = 8083
web.socket.cors.enabled = true
web.socket.cors.origin = "*"

# SSL WebSocket
web.ssl.port = 8084
web.ssl.keyfile = /path/to/server.key
web.ssl.certfile = /path/to/server.crt
```

## 🌐 跨域问题解决

### 方案1: 开发代理（推荐）
在 `vue.config.js` 中配置代理，将请求转发到MQTT服务器：

```javascript
devServer: {
  proxy: {
    '/mqtt': {
      target: 'ws://your-mqtt-server.com:8083',
      ws: true,
      changeOrigin: true
    }
  }
}
```

### 方案2: MQTT服务器CORS配置
在MQTT服务器端允许跨域访问：

```bash
# Mosquitto WebSocket CORS
http_dir /var/lib/mosquitto/www
http_cors_allow_origin "*"

# EMQ X CORS
web.socket.cors.enabled = true
web.socket.cors.origin = "*"
```

### 方案3: Nginx反向代理
```nginx
location /mqtt {
    proxy_pass http://mqtt-server:8083;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

## 📖 使用指南

### 连接MQTT服务器
1. **选择连接方式**: ws://（开发） 或 wss://（生产）
2. **填写服务器信息**: 地址、端口、路径
3. **配置认证**: 用户名和密码（可选）
4. **高级设置**: 客户端ID、超时时间等
5. **点击连接**: 测试连接或直接连接

### 发布消息
1. **输入主题**: 例如 `sensor/temperature`
2. **编写消息**: 支持文本、JSON等格式
3. **设置选项**: QoS级别、保留消息等
4. **发布消息**: 点击发布按钮

### 订阅主题
1. **输入主题**: 支持通配符 `+`（单级）和 `#`（多级）
2. **设置QoS**: 选择合适的QoS级别
3. **开始订阅**: 点击订阅按钮
4. **管理订阅**: 查看、过滤、取消订阅

### 查看日志
1. **实时监控**: 查看所有消息活动
2. **过滤消息**: 按类型、内容、主题过滤
3. **导出日志**: 支持TXT和JSON格式
4. **消息详情**: 查看完整的消息信息

## 🔌 常用MQTT测试服务器

### 公共测试服务器
```javascript
// Eclipse Mosquitto测试服务器
{
  protocol: 'ws',
  host: 'test.mosquitto.org',
  port: 8080,
  path: ''
}

// EMQ X 公共测试服务器
{
  protocol: 'ws',
  host: 'broker.emqx.io',
  port: 8083,
  path: '/mqtt'
}

// HiveMQ 公共测试服务器
{
  protocol: 'ws',
  host: 'broker.hivemq.com',
  port: 8000,
  path: '/mqtt'
}
```

## 🛠️ 开发说明

### 项目结构
```
src/
├── components/          # Vue组件
│   ├── MqttConnection.vue    # 连接管理组件
│   ├── MqttPublisher.vue     # 消息发布组件
│   ├── MqttSubscription.vue  # 订阅管理组件
│   ├── MessageLogger.vue     # 日志组件
│   └── ...
├── services/           # 服务层
│   └── MqttService.js       # MQTT服务封装
├── styles/             # 样式文件
│   └── global.css           # 全局样式
└── ...
```

### 核心技术栈
- **Vue 2.6.14**: 前端框架
- **Element UI 2.15.14**: UI组件库
- **MQTT.js 4.3.7**: MQTT客户端库
- **Vue CLI 5.0.8**: 项目构建工具

### 自定义配置
项目支持多种自定义配置：

1. **主题样式**: 修改 `src/styles/global.css`
2. **MQTT配置**: 修改 `src/services/MqttService.js`
3. **代理设置**: 修改 `vue.config.js`
4. **组件功能**: 修改对应的Vue组件

## 🐛 常见问题

### 1. 连接失败
**问题**: 无法连接到MQTT服务器
**解决方案**:
- 检查服务器地址和端口
- 确认WebSocket支持已启用
- 检查防火墙设置
- 验证认证信息

### 2. 跨域错误
**问题**: 浏览器提示跨域错误
**解决方案**:
- 使用开发代理配置
- 配置MQTT服务器CORS
- 使用反向代理

### 3. SSL证书问题
**问题**: wss连接失败，证书错误
**解决方案**:
- 确保SSL证书有效
- 检查证书链完整性
- 使用受信任的CA证书

### 4. 消息收发异常
**问题**: 消息发送失败或接收不到
**解决方案**:
- 检查主题格式
- 验证QoS设置
- 确认订阅状态
- 查看错误日志

## 📝 更新日志

### v1.0.0 (2025-06-05)
- ✅ 初始版本发布
- ✅ 完整的MQTT连接功能
- ✅ 消息发布和订阅
- ✅ 实时日志系统
- ✅ 响应式界面设计
- ✅ 跨域问题解决方案

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [MQTT.js](https://github.com/mqttjs/MQTT.js) - 优秀的MQTT客户端库
- [Element UI](https://element.eleme.io/) - 优雅的Vue组件库
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架

## 📞 联系方式

如果您有任何问题或建议，请通过以下方式联系：

- 创建 [Issue](https://github.com/your-repo/issues)
- 发送邮件: <EMAIL>

---

**🚀 开始您的MQTT测试之旅吧！**

ws://devhotel.nianjiaiot.com:38084/mgtt