<template>
  <el-card class="subscription-card">
    <div slot="header">
      <span><i class="el-icon-download"></i> 订阅管理</span>
      <el-badge :value="subscriptions.length" class="subscription-badge">
        <span style="font-size: 12px; color: #666;">活跃订阅</span>
      </el-badge>
    </div>
    
    <!-- 订阅表单 -->
    <el-form 
      :model="subscribeForm" 
      :rules="subscribeRules"
      ref="subscribeForm"
      label-width="80px" 
      size="small">
      
      <el-row :gutter="10">
        <el-col :span="14">
          <el-form-item label="主题:" prop="topic">
            <el-input 
              v-model="subscribeForm.topic" 
              placeholder="例如: sensor/+/temperature"
              @keyup.enter.native="subscribeTopic">
              <el-dropdown slot="append" @command="selectSubscribeTopic">
                <el-button icon="el-icon-arrow-down"></el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item 
                    v-for="topic in recentSubscribeTopics" 
                    :key="topic"
                    :command="topic">
                    {{ topic }}
                  </el-dropdown-item>
                  <el-dropdown-item divided command="clear">
                    <span style="color: #f56c6c;">清空历史</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-input>
            <div class="topic-tips">
              <el-tag size="mini" type="info">支持通配符: + (单级) # (多级)</el-tag>
            </div>
          </el-form-item>
        </el-col>
        
        <el-col :span="6">
          <el-form-item label="QoS:">
            <el-select v-model="subscribeForm.qos" style="width: 100%">
              <el-option label="0" :value="0"></el-option>
              <el-option label="1" :value="1"></el-option>
              <el-option label="2" :value="2"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="4">
          <el-form-item label=" ">
            <el-button 
              type="success" 
              @click="subscribeTopic"
              :loading="isSubscribing"
              icon="el-icon-download"
              style="width: 100%;">
              订阅
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <!-- 快速订阅模板 -->
    <div class="quick-subscribe">
      <el-button-group size="mini">
        <el-button @click="quickSubscribe('test/+')">测试主题</el-button>
        <el-button @click="quickSubscribe('sensor/+/data')">传感器数据</el-button>
        <el-button @click="quickSubscribe('device/+/status')">设备状态</el-button>
        <el-button @click="quickSubscribe('#')">所有消息</el-button>
      </el-button-group>
    </div>
    
    <!-- 活跃订阅列表 -->
    <el-divider content-position="left">
      <span style="color: #909399; font-size: 12px;">活跃订阅</span>
    </el-divider>
    
    <div class="subscriptions-list">
      <div 
        v-for="sub in subscriptions" 
        :key="sub.topic"
        class="subscription-item">
        <div class="subscription-info">
          <div class="subscription-topic">
            <el-tag size="small" type="success">{{ sub.topic }}</el-tag>
            <el-tag size="mini" type="info">QoS: {{ sub.qos }}</el-tag>
          </div>
          <div class="subscription-stats">
            <span class="message-count">消息: {{ sub.messageCount || 0 }}</span>
            <span class="subscribe-time">{{ sub.subscribeTime }}</span>
          </div>
        </div>
        <div class="subscription-actions">
          <el-button 
            size="mini" 
            type="text" 
            @click="toggleTopicFilter(sub.topic)"
            :class="{ 'filter-active': filteredTopics.includes(sub.topic) }"
            icon="el-icon-view">
            {{ filteredTopics.includes(sub.topic) ? '显示' : '过滤' }}
          </el-button>
          <el-button 
            size="mini" 
            type="text" 
            @click="unsubscribeTopic(sub.topic)"
            icon="el-icon-delete"
            style="color: #f56c6c;">
            取消
          </el-button>
        </div>
      </div>
      
      <div v-if="subscriptions.length === 0" class="no-subscriptions">
        <i class="el-icon-info"></i>
        <p>暂无订阅，请添加订阅主题</p>
      </div>
    </div>
    
    <!-- 订阅统计 -->
    <el-divider content-position="left">
      <span style="color: #909399; font-size: 12px;">订阅统计</span>
    </el-divider>
    
    <div class="subscription-stats">
      <div class="stat-card">
        <div class="stat-number">{{ subscriptions.length }}</div>
        <div class="stat-label">活跃订阅</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ totalMessages }}</div>
        <div class="stat-label">总消息数</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ uniqueTopics }}</div>
        <div class="stat-label">不同主题</div>
      </div>
      <div class="stat-card">
        <div class="stat-number">{{ messageRate }}</div>
        <div class="stat-label">消息/分钟</div>
      </div>
    </div>
    
    <!-- 主题过滤器 -->
    <el-divider content-position="left">
      <span style="color: #909399; font-size: 12px;">消息过滤</span>
    </el-divider>
    
    <div class="topic-filters">
      <div class="filter-header">
        <span>显示主题:</span>
        <el-button 
          size="mini" 
          type="text" 
          @click="clearAllFilters"
          v-if="filteredTopics.length > 0">
          全部显示
        </el-button>
        <el-button 
          size="mini" 
          type="text" 
          @click="hideAllTopics"
          v-if="filteredTopics.length < subscriptions.length">
          全部隐藏
        </el-button>
      </div>
      
      <div class="filter-tags" v-if="subscriptions.length > 0">
        <el-tag 
          v-for="sub in subscriptions"
          :key="sub.topic"
          size="mini"
          :type="filteredTopics.includes(sub.topic) ? 'success' : 'info'"
          :closable="false"
          @click="toggleTopicFilter(sub.topic)"
          class="filter-tag">
          {{ sub.topic }}
        </el-tag>
      </div>
    </div>
  </el-card>
</template>

<script>
export default {
  name: 'MqttSubscription',
  
  props: {
    mqttService: {
      type: Object,
      required: true
    }
  },
  
  data() {
    return {
      isSubscribing: false,
      
      subscribeForm: {
        topic: 'test/+',
        qos: 0
      },
      
      subscribeRules: {
        topic: [
          { required: true, message: '请输入订阅主题', trigger: 'blur' },
          { min: 1, max: 200, message: '主题长度在 1 到 200 个字符', trigger: 'blur' }
        ]
      },
      
      subscriptions: [],
      recentSubscribeTopics: [],
      filteredTopics: [], // 要显示的主题（其他的会被过滤）
      
      // 统计数据
      messageCounters: {}, // topic -> count
      messageTimestamps: [], // 用于计算消息频率
      startTime: Date.now()
    }
  },
  
  computed: {
    totalMessages() {
      return Object.values(this.messageCounters).reduce((sum, count) => sum + count, 0)
    },
    
    uniqueTopics() {
      return Object.keys(this.messageCounters).length
    },
    
    messageRate() {
      const now = Date.now()
      const oneMinuteAgo = now - 60000
      const recentMessages = this.messageTimestamps.filter(timestamp => timestamp > oneMinuteAgo)
      return recentMessages.length
    }
  },
  
  mounted() {
    this.loadRecentSubscribeTopics()
    this.setupMessageListener()
    
    // 定期清理旧的时间戳
    setInterval(() => {
      const oneHourAgo = Date.now() - 3600000
      this.messageTimestamps = this.messageTimestamps.filter(timestamp => timestamp > oneHourAgo)
    }, 300000) // 每5分钟清理一次
  },
  
  methods: {
    async subscribeTopic() {
      // 表单验证
      try {
        await this.$refs.subscribeForm.validate()
      } catch (error) {
        return
      }
      
      // 检查是否已经订阅
      if (this.subscriptions.find(sub => sub.topic === this.subscribeForm.topic)) {
        this.$message.warning('该主题已订阅')
        return
      }
      
      this.isSubscribing = true
      
      try {
        await this.mqttService.subscribe(this.subscribeForm.topic, {
          qos: this.subscribeForm.qos
        })
        
        // 添加到订阅列表
        const subscription = {
          topic: this.subscribeForm.topic,
          qos: this.subscribeForm.qos,
          subscribeTime: new Date().toLocaleTimeString(),
          messageCount: 0
        }
        
        this.subscriptions.push(subscription)
        this.filteredTopics.push(this.subscribeForm.topic) // 默认显示新订阅
        
        this.$message.success(`订阅成功: ${this.subscribeForm.topic}`)
        this.$emit('message-log', 'success', `订阅主题: ${this.subscribeForm.topic}`)
        
        // 保存到最近使用
        this.addRecentSubscribeTopic(this.subscribeForm.topic)
        
        // 清空表单
        this.subscribeForm.topic = ''
        
      } catch (error) {
        this.$message.error(`订阅失败: ${error.message}`)
        this.$emit('message-log', 'error', `订阅失败: ${error.message}`)
      }
      
      this.isSubscribing = false
    },
    
    async unsubscribeTopic(topic) {
      try {
        await this.mqttService.unsubscribe(topic)
        
        // 从列表中移除
        this.subscriptions = this.subscriptions.filter(sub => sub.topic !== topic)
        this.filteredTopics = this.filteredTopics.filter(t => t !== topic)
        
        this.$message.success(`取消订阅成功: ${topic}`)
        this.$emit('message-log', 'warning', `取消订阅: ${topic}`)
        
      } catch (error) {
        this.$message.error(`取消订阅失败: ${error.message}`)
        this.$emit('message-log', 'error', `取消订阅失败: ${error.message}`)
      }
    },
    
    quickSubscribe(topic) {
      this.subscribeForm.topic = topic
      this.subscribeTopic()
    },
    
    selectSubscribeTopic(topic) {
      if (topic === 'clear') {
        this.recentSubscribeTopics = []
        this.saveRecentSubscribeTopics()
        this.$message.success('历史主题已清空')
      } else {
        this.subscribeForm.topic = topic
      }
    },
    
    toggleTopicFilter(topic) {
      const index = this.filteredTopics.indexOf(topic)
      if (index > -1) {
        this.filteredTopics.splice(index, 1)
      } else {
        this.filteredTopics.push(topic)
      }
      
      // 向父组件发送过滤更新事件
      this.$emit('filter-change', this.filteredTopics)
    },
    
    clearAllFilters() {
      this.filteredTopics = this.subscriptions.map(sub => sub.topic)
      this.$emit('filter-change', this.filteredTopics)
    },
    
    hideAllTopics() {
      this.filteredTopics = []
      this.$emit('filter-change', this.filteredTopics)
    },
    
    setupMessageListener() {
      if (this.mqttService) {
        this.mqttService.onMessage((topic, message) => {
          // 更新消息计数
          if (!this.messageCounters[topic]) {
            this.messageCounters[topic] = 0
          }
          this.messageCounters[topic]++
          
          // 更新订阅项的消息计数
          const subscription = this.subscriptions.find(sub => this.topicMatches(topic, sub.topic))
          if (subscription) {
            subscription.messageCount = (subscription.messageCount || 0) + 1
          }
          
          // 记录时间戳用于计算频率
          this.messageTimestamps.push(Date.now())
          
          // 触发消息接收事件
          console.log(`Received message on topic: ${topic}`, message.toString())
        })
      }
    },
    
    topicMatches(actualTopic, subscriptionTopic) {
      // 简单的MQTT主题匹配逻辑
      const subParts = subscriptionTopic.split('/')
      const actualParts = actualTopic.split('/')
      
      // 检查多级通配符 #
      const hashIndex = subParts.indexOf('#')
      if (hashIndex !== -1) {
        // # 必须是最后一个部分
        if (hashIndex === subParts.length - 1) {
          return actualParts.slice(0, hashIndex).every((part, i) => 
            subParts[i] === '+' || subParts[i] === part
          )
        }
      }
      
      // 检查长度
      if (subParts.length !== actualParts.length) {
        return false
      }
      
      // 逐部分检查
      return subParts.every((subPart, i) => 
        subPart === '+' || subPart === actualParts[i]
      )
    },
    
    addRecentSubscribeTopic(topic) {
      this.recentSubscribeTopics = this.recentSubscribeTopics.filter(t => t !== topic)
      this.recentSubscribeTopics.unshift(topic)
      if (this.recentSubscribeTopics.length > 10) {
        this.recentSubscribeTopics = this.recentSubscribeTopics.slice(0, 10)
      }
      this.saveRecentSubscribeTopics()
    },
    
    saveRecentSubscribeTopics() {
      try {
        localStorage.setItem('mqtt_recent_subscribe_topics', JSON.stringify(this.recentSubscribeTopics))
      } catch (error) {
        console.error('保存最近订阅主题失败:', error)
      }
    },
    
    loadRecentSubscribeTopics() {
      try {
        const saved = localStorage.getItem('mqtt_recent_subscribe_topics')
        if (saved) {
          this.recentSubscribeTopics = JSON.parse(saved)
        }
      } catch (error) {
        console.error('加载最近订阅主题失败:', error)
        this.recentSubscribeTopics = []
      }
    }
  }
}
</script>

<style scoped>
.subscription-card {
  margin-bottom: 20px;
}

.subscription-badge {
  margin-left: 10px;
}

.topic-tips {
  margin-top: 5px;
}

.quick-subscribe {
  margin: 15px 0;
  text-align: center;
}

.subscriptions-list {
  max-height: 300px;
  overflow-y: auto;
}

.subscription-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #fafafa;
}

.subscription-info {
  flex: 1;
}

.subscription-topic {
  margin-bottom: 5px;
}

.subscription-topic .el-tag {
  margin-right: 8px;
}

.subscription-stats {
  font-size: 12px;
  color: #666;
  display: flex;
  gap: 15px;
}

.subscription-actions {
  display: flex;
  gap: 5px;
}

.filter-active {
  color: #67c23a !important;
}

.no-subscriptions {
  text-align: center;
  color: #909399;
  padding: 30px;
}

.no-subscriptions i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}

.subscription-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 10px;
  margin: 15px 0;
}

.stat-card {
  text-align: center;
  padding: 15px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.topic-filters {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 13px;
  color: #666;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.filter-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.filter-tag:hover {
  transform: scale(1.05);
}

.el-divider {
  margin: 15px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .subscription-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .subscription-actions {
    align-self: flex-end;
  }
  
  .subscription-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filter-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>