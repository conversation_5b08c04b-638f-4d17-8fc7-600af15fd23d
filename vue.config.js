const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  
  devServer: {
    port: 8080,
    host: '0.0.0.0',
    
    proxy: {
      '/mqtt': {
        target: 'ws://**************:38084',
        ws: true,
        changeOrigin: true,
        secure: false,
        logLevel: 'debug'
      }
    },
    
    allowedHosts: 'all'
  },
  
  configureWebpack: {
    resolve: {
      fallback: {
        "url": require.resolve("url/"),
        "util": require.resolve("util/"),
        "buffer": require.resolve("buffer"),
        "process": require.resolve("process/browser"),
        "stream": require.resolve("stream-browserify"),
        "path": require.resolve("path-browserify"),
        "fs": false,
        "net": false,
        "tls": false,
        "crypto": false,
        "http": false,
        "https": false,
        "events": false
      }
    },
    plugins: [
      new (require('webpack')).ProvidePlugin({
        process: 'process/browser',
        Buffer: ['buffer', 'Buffer']
      })
    ]
  },
  
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/'
})